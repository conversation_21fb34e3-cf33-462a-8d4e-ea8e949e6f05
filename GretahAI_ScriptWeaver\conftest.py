"""
Pytest configuration file for GretahAI ScriptWeaver automated test framework.

This module provides fixtures, hooks, and utilities for UI test automation with Selenium.
It handles browser instantiation, test logging, screenshot capture, and reporting.

Key Features:
- WebDriver fixture management with automatic cleanup
- Screenshot capture on test failures
- Test logging and artifact collection
- Performance monitoring and metrics collection
- Compatibility with GRETAH Test Insight patterns

Fixtures:
    setup_session: Session-level fixture for test session setup and teardown
    browser: Creates and manages a WebDriver instance with automatic cleanup
    driver: Compatibility alias for the browser fixture (main fixture used by generated scripts)
    take_screenshot_fixture: Allows taking screenshots during test execution
    log_message: Enables adding custom log messages from tests

Hooks:
    pytest_runtest_makereport: Captures test artifacts (screenshots, page source)
    pytest_terminal_summary: Generates test execution summary report
"""

import pytest
import logging
import os
import time
import json
import traceback
from datetime import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# ================================
# Directory Setup
# ================================
# Create necessary directories for test artifacts
SCREENSHOT_DIR = Path("screenshots")
LOG_DIR = Path("logs")
TEST_LOGS_DIR = LOG_DIR / "test_logs"
PAGE_SOURCE_DIR = LOG_DIR / "page_sources"

# Ensure directories exist
for directory in [SCREENSHOT_DIR, LOG_DIR, TEST_LOGS_DIR, PAGE_SOURCE_DIR]:
    directory.mkdir(exist_ok=True)

# ================================
# Logging Configuration
# ================================
def setup_logging():
    """Configure logging for test execution"""
    log_file = LOG_DIR / f"test_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    # Configure logging with UTF-8 encoding
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger("ScriptWeaver.TestLogger")

# Initialize logger
logger = setup_logging()

# ================================
# Utility Functions
# ================================
def take_screenshot(driver, name="screenshot"):
    """
    Take a screenshot and save it to the screenshots directory.

    Args:
        driver: WebDriver instance
        name: Name for the screenshot file

    Returns:
        str: Path to the saved screenshot
    """
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = SCREENSHOT_DIR / f"{name}_{timestamp}.png"

        if driver.save_screenshot(str(screenshot_path)):
            logger.info(f"Screenshot saved: {screenshot_path}")
            return str(screenshot_path)
        else:
            logger.error(f"Failed to save screenshot: {screenshot_path}")
            return None
    except Exception as e:
        logger.error(f"Error taking screenshot: {e}")
        return None

def save_page_source(driver, name="page_source"):
    """
    Save the current page source to a file.

    Args:
        driver: WebDriver instance
        name: Name for the page source file

    Returns:
        str: Path to the saved page source file
    """
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        source_path = PAGE_SOURCE_DIR / f"{name}_{timestamp}.html"

        with open(source_path, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)

        logger.info(f"Page source saved: {source_path}")
        return str(source_path)
    except Exception as e:
        logger.error(f"Error saving page source: {e}")
        return None

def add_test_log_handler(test_node):
    """
    Add a test-specific log handler.

    Args:
        test_node: Pytest test node

    Returns:
        tuple: (handler, log_file_path)
    """
    test_name = test_node.name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file_path = TEST_LOGS_DIR / f"{test_name}_{timestamp}.log"

    # Create file handler for this specific test
    handler = logging.FileHandler(log_file_path, encoding='utf-8')
    handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)

    # Add handler to the logger
    logger.addHandler(handler)

    return handler, str(log_file_path)

# ================================
# Pytest Fixtures
# ================================
@pytest.fixture(scope="session", autouse=True)
def setup_session():
    """Session-level fixture that runs automatically for all tests"""
    logger.info("Starting GretahAI ScriptWeaver test session")
    yield
    logger.info("GretahAI ScriptWeaver test session finished")

@pytest.fixture(scope="function")
def browser(request):
    """
    Browser fixture with automatic logging and artifact path tracking.

    This fixture creates a Chrome WebDriver instance with proper configuration
    for test automation, including screenshot capture and logging.
    """
    # Ensure user_properties exists on the node
    if not hasattr(request.node, 'user_properties'):
        request.node.user_properties = []

    # Create test-specific log file
    test_log_handler, log_file_path = add_test_log_handler(request.node)
    request.node.user_properties.append(("artifact_log", log_file_path))

    test_name = request.node.function.__name__
    logger.info(f"Starting browser for test: {test_name}")

    # Configure Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")

    # Enable performance logging
    chrome_options.set_capability("goog:loggingPrefs", {
        'browser': 'ALL',
        'performance': 'ALL'
    })

    driver = None
    try:
        # Initialize WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)  # 10 second implicit wait
        driver.set_page_load_timeout(30)  # 30 second page load timeout

        logger.info("Browser started successfully")
        driver.test_name = request.node.name

        yield driver

        # Teardown
        logger.info(f"Finishing test: {test_name}")

        # Log browser console messages
        try:
            browser_logs = driver.get_log('browser')
            if browser_logs:
                logger.info("Browser console logs:")
                for log in browser_logs:
                    logger.info(f"  {log}")
        except Exception as e:
            logger.warning(f"Could not retrieve browser logs: {e}")

    except Exception as e:
        logger.error(f"Failed to initialize browser: {e}")
        logger.error(traceback.format_exc())
        raise
    finally:
        try:
            if driver:
                driver.quit()
                logger.info("Browser closed successfully")
        except Exception as e:
            logger.error(f"Failed to close browser: {e}")

        # Remove the test-specific log handler
        try:
            logger.removeHandler(test_log_handler)
            test_log_handler.close()
        except Exception as e:
            logger.warning(f"Failed to remove test log handler: {e}")

@pytest.fixture
def driver(browser):
    """
    Compatibility alias for the browser fixture.

    This is the main fixture that generated test scripts will use.
    It provides the same WebDriver instance as the browser fixture.
    """
    return browser

@pytest.fixture
def take_screenshot_fixture(driver):
    """
    Fixture to take screenshots during test execution.

    Usage: Call the returned function with a name parameter
    """
    def _take_screenshot(name):
        return take_screenshot(driver, name)

    return _take_screenshot

@pytest.fixture
def log_message():
    """Fixture to add custom log messages from tests"""
    def _log_message(message, level="INFO"):
        level = level.upper()
        if level == "INFO":
            logger.info(message)
        elif level == "WARNING":
            logger.warning(message)
        elif level == "ERROR":
            logger.error(message)
        elif level == "DEBUG":
            logger.debug(message)
        else:
            logger.info(message)

    return _log_message

# ================================
# Pytest Hooks
# ================================
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    Hook to capture test artifacts and handle test failures.

    This hook automatically takes screenshots and saves page source
    when tests fail, providing valuable debugging information.
    """
    outcome = yield
    report = outcome.get_result()

    # Capture artifacts during test execution phase
    if call.when == "call":
        driver = None
        if "driver" in item.fixturenames or "browser" in item.fixturenames:
            try:
                # Try to get the driver from either fixture name
                driver = item.funcargs.get("driver") or item.funcargs.get("browser")
            except Exception as e:
                logger.error(f"Could not retrieve driver fixture for {item.nodeid}: {e}")

        if driver and report.failed:
            # Test failed - capture artifacts
            logger.error(f"Test failed: {item.nodeid}")

            # Take failure screenshot
            screenshot_path = take_screenshot(driver, f"{item.name}_failure")
            if screenshot_path:
                report.user_properties.append(("screenshot", screenshot_path))

            # Save page source
            source_path = save_page_source(driver, f"{item.name}_failure")
            if source_path:
                report.user_properties.append(("page_source", source_path))

            # Log current URL
            try:
                current_url = driver.current_url
                logger.error(f"Test failed on URL: {current_url}")
                report.user_properties.append(("failure_url", current_url))
            except Exception as e:
                logger.warning(f"Could not get current URL: {e}")

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_terminal_summary(terminalreporter, exitstatus=None, config=None):
    """
    Create a summary report at the end of the test session.
    """
    yield

    try:
        # Collect test statistics
        stats = {
            'total': len(terminalreporter.stats.get('passed', [])) +
                    len(terminalreporter.stats.get('failed', [])) +
                    len(terminalreporter.stats.get('skipped', [])),
            'passed': len(terminalreporter.stats.get('passed', [])),
            'failed': len(terminalreporter.stats.get('failed', [])),
            'skipped': len(terminalreporter.stats.get('skipped', [])),
            'duration': time.time() - terminalreporter._sessionstarttime
        }

        # Save summary to JSON file (skip logging to avoid closed file issues)
        summary_file = LOG_DIR / f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(stats, f, indent=2)

        # Use a separate logger to avoid conflicts
        import logging
        summary_logger = logging.getLogger("test_framework")
        summary_logger.info(f"Test summary saved to {summary_file}")

    except Exception as e:
        # Silently handle any logging errors during shutdown
        pass
