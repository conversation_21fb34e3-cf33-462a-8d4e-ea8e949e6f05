"""
Stage 7: Test Script Execution

This module handles Phase 7 of the GretahAI ScriptWeaver application workflow.
Stage 7 is responsible for executing the generated test scripts and managing
the progression through test case steps.

Key Features:
- Test script execution using pytest with proper environment configuration
- Screenshot capture and display functionality
- Automatic advancement to next test case step after successful execution
- Combined script creation for completed test cases
- Auto-transition preferences and workflow management
- Proper handling of test results (success/failure scenarios)
- Integration with Stage 8 for script optimization
- Enhanced error handling with workflow pause and user acknowledgment
- Detailed error reporting with retry and continue options
- Error state management through StateManager

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions (Stage 7 → Stage 8 or Stage 7 → Stage 3)

Functions:
    stage7_run_script(state): Main Stage 7 function for test script execution
    create_combined_script(state): Helper function to create combined scripts
    advance_to_next_step(): Helper function to advance to the next test step
"""

import os
import logging
import streamlit as st
import subprocess
import time
from pathlib import Path
from datetime import datetime

# Note: AI functions for script merging are now handled in Stage 8 optimization

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage7")


# Note: create_combined_script function moved to Stage 6 (stages/stage6.py)
# where script generation logically belongs


def advance_to_next_step():
    """
    Advance to the next test case step.

    This function:
    1. Increments the current step index
    2. Selects the next test case step from the step table
    3. Updates state manager variables
    4. Returns True if there are more steps, False if all steps are processed

    Returns:
        bool: True if successfully advanced to the next test case step, False if all steps are processed
    """
    logger.info("=== advance_to_next_step() called ===")

    # Get state manager
    from state_manager import StateManager
    state = StateManager.get(st)

    # Check if we have a step table
    if not state.step_table_json or not isinstance(state.step_table_json, list):
        logger.warning("No valid step_table_json found")
        return False

    # Get the total number of steps if not already set
    if state.total_steps == 0:
        state.total_steps = len(state.step_table_json)
        logger.info(f"State change: total_steps = {state.total_steps}")

    # Log detailed state information before advancing
    logger.info(f"State before advancing: current_step_index = {state.current_step_index}")
    logger.info(f"State before advancing: total_steps = {state.total_steps}")
    logger.info(f"State before advancing: step_ready_for_script = {state.step_ready_for_script}")
    logger.info(f"State before advancing: all_steps_done = {state.all_steps_done}")

    # Also log to Streamlit session state for debugging
    st.session_state['debug_before_advance'] = {
        'current_step_index': state.current_step_index,
        'total_steps': state.total_steps,
        'step_ready_for_script': state.step_ready_for_script,
        'all_steps_done': state.all_steps_done,
        'timestamp': datetime.now().strftime("%H:%M:%S.%f")
    }

    # Save the current step information before advancing
    current_step_info = None
    if hasattr(state, 'selected_step') and state.selected_step:
        current_step_info = {
            'step_no': state.selected_step.get('Step No', 'Unknown'),
            'test_steps': state.selected_step.get('Test Steps', ''),
            'expected_result': state.selected_step.get('Expected Result', '')
        }
        logger.info(f"Current step before advancing: {current_step_info['step_no']}")

    # Use the state manager's update method to increment the step index
    new_step_index = state.current_step_index + 1
    state.update_step_progress(current_step_index=new_step_index)

    # Check if we've processed all steps
    if new_step_index >= state.total_steps:
        logger.info("All steps processed, setting all_steps_done to True")
        # Set all_steps_done to True only if we're actually on the last step
        # This ensures we don't prematurely mark all steps as done
        if state.current_step_index == state.total_steps - 1:
            state.update_step_progress(all_steps_done=True)
            logger.info(f"Setting all_steps_done=True on last step (index {state.current_step_index}, total {state.total_steps})")
        else:
            logger.warning(f"Not setting all_steps_done=True because we're not on the last step (index {state.current_step_index}, total {state.total_steps})")

        # Add a message to session state for display after rerun
        st.session_state['stage_progression_message'] = "✅ All test case steps have been processed!"

        # All steps completed - workflow will show manual progression options in Stage 7 UI
        logger.info("All steps completed - user will choose next action in Stage 7 UI")

        # Force state update in session state
        st.session_state['state'] = state

        # Immediately rerun to refresh the UI with the new state
        st.rerun()
        # Return statement will never be reached due to rerun, but included for clarity
        return False

    # Get the next step from the step table
    next_step = state.step_table_json[state.current_step_index]
    logger.info(f"Next step: step_no = {next_step.get('step_no')}, action = {next_step.get('action')}")

    # Find the corresponding original step
    if state.selected_test_case and 'Steps' in state.selected_test_case:
        original_steps = state.selected_test_case.get('Steps', [])
        step_no = str(next_step.get('step_no', ''))
        logger.info(f"Looking for original step with Step No: {step_no}")

        try:
            selected_original_step = next(
                (step for step in original_steps if str(step.get('Step No')) == step_no),
                None
            )

            if selected_original_step:
                logger.info(f"Found original step: Step No = {selected_original_step.get('Step No')}")
            else:
                logger.warning(f"Original step not found for step_no: {step_no}")
                logger.warning(f"Available steps: {[str(step.get('Step No')) for step in original_steps]}")
        except Exception as e:
            logger.error(f"Error finding original step: {e}")
            selected_original_step = None

        if selected_original_step and next_step:
            # Save context from the current step before updating state
            if current_step_info:
                # Mark the current step as completed
                current_step_no = current_step_info['step_no']

                # Initialize completed_steps if not present
                if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                    state.completed_steps = []

                # Add current step to completed steps if not already there
                if current_step_no not in state.completed_steps:
                    state.completed_steps.append(current_step_no)
                    logger.info(f"State change: added step {current_step_no} to completed_steps")

                # Store context from the current step that might be useful for the next step
                if not hasattr(state, 'step_context') or not isinstance(state.step_context, dict):
                    state.step_context = {}

                # Save relevant context from the current step
                state.step_context[current_step_no] = {
                    "elements": state.step_elements if hasattr(state, 'step_elements') else [],
                    "matches": state.step_matches if hasattr(state, 'step_matches') else {},
                    "test_data": state.test_data if hasattr(state, 'test_data') else {},
                    "script_path": state.generated_script_path if hasattr(state, 'generated_script_path') else None
                }
                # Log state change for context saving
                previous_context = state.step_context.get(current_step_no, None)
                if previous_context != state.step_context[current_step_no]:
                    logger.info(f"State change: saved context for step {current_step_no}")

            # Update state manager with the new step
            state.selected_step_table_entry = next_step
            state.selected_step = selected_original_step
            # Log state change for step selection
            previous_step_no = state.selected_step.get('Step No') if hasattr(state, 'selected_step') and state.selected_step else None
            new_step_no = selected_original_step.get('Step No')
            if previous_step_no != new_step_no:
                logger.info(f"State change: updated selected_step_table_entry and selected_step to Step No {new_step_no}")

            # Reset step-specific state variables
            state.step_elements = []
            state.step_matches = {}
            state.test_data = {}
            state.test_data_skipped = False
            state.llm_step_analysis = {}
            state.step_ready_for_script = False
            state.script_just_generated = False
            # Only log if there were actual changes to reset
            if (hasattr(state, 'step_elements') and state.step_elements) or \
               (hasattr(state, 'step_matches') and state.step_matches) or \
               (hasattr(state, 'test_data') and state.test_data) or \
               (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or \
               (hasattr(state, 'llm_step_analysis') and state.llm_step_analysis) or \
               (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or \
               (hasattr(state, 'script_just_generated') and state.script_just_generated):
                logger.info("State change: reset step-specific state variables")

            # Log detailed state information after advancing
            logger.info(f"State after advancing: current_step_index = {state.current_step_index}")
            logger.info(f"State after advancing: total_steps = {state.total_steps}")
            logger.info(f"State after advancing: step_ready_for_script = {state.step_ready_for_script}")
            logger.info(f"State after advancing: all_steps_done = {state.all_steps_done}")
            logger.info(f"State after advancing: selected_step.Step No = {state.selected_step.get('Step No')}")

            # Also log to Streamlit session state for debugging
            st.session_state['debug_after_advance'] = {
                'current_step_index': state.current_step_index,
                'total_steps': state.total_steps,
                'step_ready_for_script': state.step_ready_for_script,
                'all_steps_done': state.all_steps_done,
                'selected_step_no': state.selected_step.get('Step No'),
                'timestamp': datetime.now().strftime("%H:%M:%S.%f")
            }

            # Add a message to session state for display after rerun
            next_step_no = selected_original_step.get('Step No')
            next_step_action = selected_original_step.get('Test Steps', '')[:50]  # Truncate for display
            st.session_state['stage_progression_message'] = f"✅ Advanced to Test Case Step {next_step_no}: {next_step_action}..."

            # Force the state to be updated in the session state
            st.session_state['state'] = state

            logger.info(f"Successfully advanced to next step: {next_step_no}")
            # Immediately rerun to refresh the UI with the new state
            st.rerun()
            # Return statement will never be reached due to rerun, but included for clarity
            return True
        else:
            logger.warning(f"Failed to advance: selected_original_step={selected_original_step is not None}, next_step={next_step is not None}")
    else:
        logger.warning("No selected_test_case or no Steps in selected_test_case")

    logger.warning("Failed to advance to next step")
    return False


def stage7_run_script(state):
    """
    Phase 7: Run Test Script for Selected Test Case Step.

    This stage allows the user to run the generated test script for the selected test case step.
    It checks if a script has been generated, displays the script, and provides a simple button
    to run the test. When the user clicks the button, it executes the script using pytest and
    displays the results, including any screenshots captured during the test run.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 7: Run Test Script</h2>", unsafe_allow_html=True)

    # Check for execution errors that need user acknowledgment
    if (_has_unacknowledged_error(state)):
        _display_error_acknowledgment_ui(state)
        return

    if not hasattr(state, 'generated_script_path') or not state.generated_script_path:
        st.warning("⚠️ Please generate a test script in Phase 6 first")
        return

    # Check if all steps are completed BEFORE showing the run button
    # This ensures the Stage 8 transition UI is always visible when appropriate
    if hasattr(state, 'all_steps_done') and state.all_steps_done:
        logger.info("Stage 7: All steps completed, showing Stage 8 transition options")

        st.success("✅ All test case steps have been processed!")

        # Note: Combined script creation is now handled in Stage 6
        # Display combined script if it exists from Stage 6
        if hasattr(state, 'combined_script_path') and state.combined_script_path:
            st.success(f"📄 Combined script available: {os.path.basename(state.combined_script_path)}")
            with st.expander("View Combined Script", expanded=False):
                if hasattr(state, 'combined_script_content') and state.combined_script_content:
                    st.code(state.combined_script_content, language="python")
                else:
                    # Load from file if content not in state
                    try:
                        with open(state.combined_script_path, 'r') as f:
                            combined_script_content = f.read()
                        st.code(combined_script_content, language="python")
                    except Exception as e:
                        st.error(f"Error reading combined script: {e}")
                st.info(f"Combined script file: {state.combined_script_path}")
        else:
            st.info("📄 Combined script will be created automatically in Stage 6")

        # Show workflow completion message and manual progression options
        st.markdown("""
        <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
            <p style="font-size: 16px; color: #4CAF50; margin: 0;">All steps completed for this test case</p>
            <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Choose your next action below</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("### Choose Next Action")
        st.markdown("You can either optimize the combined script or return to select a new test case:")

        # Create two columns for the progression options
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Recommended:** Optimize your test script")
            st.markdown("Creates a production-ready, consolidated test suite")
            if st.button("Proceed to Script Optimization (Phase 8)", use_container_width=True, type="primary"):
                logger.info("User chose to proceed to Script Optimization (Phase 8)")
                # Set a flag to indicate we're transitioning from Stage 7 to Stage 8
                st.session_state['transitioning_to_stage8'] = True
                st.session_state['stage_progression_message'] = "✅ All steps completed. Proceeding to Script Optimization (Phase 8)."

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

        with col2:
            st.markdown("**Alternative:** Skip optimization")
            st.markdown("Return to test case selection immediately")
            if st.button("Return to Test Case Selection (Phase 3)", use_container_width=True):
                logger.info("User chose to skip optimization and return to Phase 3")
                # Reset test case state with confirmation
                state.reset_test_case_state(confirm=True, reason="User chose to skip optimization and return to Phase 3")

                # Set a flag to indicate we're transitioning from Stage 7 to Stage 3
                st.session_state['transitioning_to_stage3'] = True
                st.session_state['stage_progression_message'] = "✅ All steps completed. Returning to Phase 3 to select a new test case."

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

        # Return early to prevent showing the run button when all steps are done
        return

    # Create two columns for script info and test status
    col1, col2 = st.columns([3, 2])

    with col1:
        st.success(f"✓ Script ready: {os.path.basename(state.generated_script_path)}")

    with col2:
        # Show test case step info
        step_no = state.selected_step.get('Step No', 'Unknown')
        st.info(f"Test Case Step: {step_no}")

    # Show information when we're on the last step
    if state.current_step_index == state.total_steps - 1:
        st.info("📌 This is the last step in the test case.")

    # Display the generated script in a collapsible section
    if hasattr(state, 'last_script_content') and state.last_script_content:
        with st.expander("View Test Script", expanded=False):
            st.code(state.last_script_content, language="python")
            if hasattr(state, 'last_script_file') and state.last_script_file:
                st.info(f"Script file: {state.last_script_file}")

    # Add a visual indicator to encourage running the script
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Click the button below to run the test script</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">This will open a browser window to execute the test</p>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced run button with comprehensive pytest execution
    if st.button("Run Test Script", disabled=not state.generated_script_path, use_container_width=True):
        with st.spinner(f"Running test script for Step {state.selected_step.get('Step No')}..."):
            try:
                # Import the JUnit parser
                from core.junit_parser import parse_junit_xml, format_test_results_for_display

                # Set environment variables for the test run
                env = os.environ.copy()
                # Always run in visible mode (not headless)
                env["HEADLESS"] = "0"

                # Generate timestamped result file name
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_xml_path = f"results_{timestamp}.xml"

                # Enhanced pytest command with comprehensive options (matching Test Insight)
                pytest_command = [
                    "pytest",
                    state.generated_script_path,
                    f"--junitxml={result_xml_path}",
                    "--log-cli-level=DEBUG",
                    "--capture=sys",
                    "--tb=short",
                    "-v"
                ]

                logger.info(f"Stage 7: Executing pytest command: {' '.join(pytest_command)}")

                # Run the test script with enhanced configuration
                result = subprocess.run(
                    pytest_command,
                    capture_output=True, text=True,
                    env=env,
                    cwd=os.getcwd()  # Ensure we're in the correct directory
                )

                # Parse JUnit XML results if available
                xml_results = None
                performance_metrics = {}
                artifacts = {}

                if os.path.exists(result_xml_path):
                    xml_results = parse_junit_xml(result_xml_path)
                    if xml_results:
                        formatted_results = format_test_results_for_display(xml_results)
                        performance_metrics = formatted_results.get("performance_summary", {})

                        # Extract artifacts from test details
                        for test_detail in formatted_results.get("test_details", []):
                            test_artifacts = test_detail.get("artifacts", {})
                            if test_artifacts:
                                artifacts.update(test_artifacts)

                # Store comprehensive test results
                state.test_results = {
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode,
                    "step_no": state.selected_step.get('Step No'),
                    "test_case_id": state.selected_test_case.get('Test Case ID'),
                    "xml_path": result_xml_path if os.path.exists(result_xml_path) else None,
                    "xml_results": xml_results,
                    "performance_metrics": performance_metrics,
                    "artifacts": artifacts,
                    "timestamp": timestamp
                }

                # Display the test results with enhanced information
                if result.returncode == 0:
                    st.success(f"✅ Test Case Step {state.selected_step.get('Step No')} test passed!")

                    # Set the step_ready_for_script flag to True after successful test execution
                    if not state.step_ready_for_script:
                        state.step_ready_for_script = True
                        logger.info("State change: step_ready_for_script = True (after successful test execution)")

                    # Force state update in session state
                    st.session_state['state'] = state
                else:
                    # Test execution failed - set error state and pause workflow
                    st.error(f"❌ Test Case Step {state.selected_step.get('Step No')} test failed. See details below.")

                    # Prepare error details for state management
                    error_details = {
                        'error_message': f"Test Case Step {state.selected_step.get('Step No')} execution failed",
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'timestamp': timestamp,
                        'step_no': state.selected_step.get('Step No', 'Unknown'),
                        'script_path': state.generated_script_path
                    }

                    # Set error state to pause workflow
                    _set_execution_error_safe(state, error_details)

                    # Force state update in session state
                    st.session_state['state'] = state

                # Display comprehensive test results
                with st.expander("Test Execution Results", expanded=True):
                    # Basic execution info
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Exit Code", result.returncode)
                    with col2:
                        st.metric("Timestamp", timestamp)
                    with col3:
                        if xml_results and "summary" in xml_results:
                            duration = xml_results["summary"].get("duration", 0)
                            st.metric("Duration", f"{duration:.2f}s")

                    # Performance metrics if available
                    if performance_metrics and "aggregated" in performance_metrics:
                        st.subheader("Performance Metrics")
                        perf_cols = st.columns(4)
                        metrics_data = performance_metrics["aggregated"]

                        if "execution_time" in metrics_data:
                            with perf_cols[0]:
                                st.metric("Execution Time", f"{metrics_data['execution_time']['average']:.3f}s")
                        if "memory_usage" in metrics_data:
                            with perf_cols[1]:
                                st.metric("Memory Usage", f"{metrics_data['memory_usage']['average']:.1f}MB")
                        if "cpu_usage" in metrics_data:
                            with perf_cols[2]:
                                st.metric("CPU Usage", f"{metrics_data['cpu_usage']['average']:.1f}%")
                        if "network_requests" in metrics_data:
                            with perf_cols[3]:
                                st.metric("Network Requests", int(metrics_data['network_requests']['total']))

                    # Console output
                    st.subheader("Console Output")
                    if result.stdout:
                        st.code(result.stdout, language="text")

                    if result.stderr:
                        st.subheader("Error Output")
                        st.code(result.stderr, language="text")

                    # Artifacts section
                    if artifacts:
                        st.subheader("Test Artifacts")
                        for artifact_type, artifact_path in artifacts.items():
                            if os.path.exists(artifact_path):
                                if artifact_type == "screenshot":
                                    st.image(artifact_path, caption=f"Screenshot: {os.path.basename(artifact_path)}")
                                elif artifact_type == "log":
                                    with st.expander(f"View Log: {os.path.basename(artifact_path)}"):
                                        try:
                                            with open(artifact_path, 'r', encoding='utf-8') as f:
                                                st.code(f.read(), language="text")
                                        except Exception as e:
                                            st.error(f"Could not read log file: {e}")
                                else:
                                    st.info(f"{artifact_type.title()}: {artifact_path}")
                            else:
                                st.warning(f"{artifact_type.title()} file not found: {artifact_path}")

                # Check for screenshots in the screenshots directory
                screenshots_dir = Path("screenshots")
                if screenshots_dir.exists():
                    screenshot_files = list(screenshots_dir.glob("*.png"))
                    if screenshot_files:
                        # Get the most recent screenshot
                        latest_screenshot = max(screenshot_files, key=os.path.getmtime)

                        # Check if it was created recently (within the last minute)
                        import time
                        if time.time() - os.path.getmtime(latest_screenshot) < 60:
                            with st.expander("Latest Screenshot", expanded=True):
                                st.image(str(latest_screenshot), caption=f"Screenshot: {latest_screenshot.name}")
                                st.info(f"Screenshot saved to: {latest_screenshot}")

                logger.info(f"Stage 7: Test execution completed with return code {result.returncode}")

                # Check if execution failed and workflow should be paused
                if _has_unacknowledged_error(state):
                    logger.info("Stage 7: Execution error occurred, pausing workflow for user acknowledgment")
                    # Don't proceed with automatic advancement - let user acknowledge error first
                    return

                # Set flags to indicate the current step is completed and ready for next step
                # Check if there are more steps
                next_step_index = state.current_step_index + 1
                if next_step_index < state.total_steps:
                        # Get the next step information
                        next_step = state.step_table_json[next_step_index]
                        next_step_no = next_step.get('step_no', 'N/A')

                        # Mark Stage 7 as complete
                        st.success(f"✅ Test Case Step {state.selected_step.get('Step No')} completed successfully!")
                        st.info("Automatically advancing to the next test case step...")

                        # Add a small delay to allow the user to see the success message
                        time.sleep(1.5)

                        # Store current step completion status
                        current_step_no = state.selected_step.get('Step No')
                        if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                            state.completed_steps = []
                        if current_step_no not in state.completed_steps:
                            state.completed_steps.append(current_step_no)
                            logger.info(f"State change: added step {current_step_no} to completed_steps")

                        # The flag for script generation should already be set to True after test execution
                        # Double-check to make sure it's set
                        if not state.step_ready_for_script:
                            state.step_ready_for_script = True
                            logger.info("State change: step_ready_for_script = True (before automatic advancement)")

                        # Log the automatic advancement
                        logger.info(f"Automatically advancing to next step after test execution: {next_step_no}")

                        # Set up session state variables before calling advance_to_next_step
                        # since it will trigger a rerun if successful

                        # Set a message to be displayed in Stage 4
                        st.session_state['stage_progression_message'] = f"✅ Test Case Step {current_step_no} completed and automatically advanced to Step {next_step_no}"

                        # Add debug information to track the state update
                        st.session_state['auto_advance_debug'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'from_step': current_step_no,
                            'to_step': next_step_no,
                            'current_step_index': state.current_step_index
                        }

                        # Set a flag to force a refresh after advancement
                        st.session_state['force_refresh_after_advance'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'target_step': next_step_no,
                            'from_step': current_step_no
                        }

                        # Set a flag to indicate we're coming back from Stage 7
                        # This will be used in run_app() to stop at Stage 4
                        st.session_state['coming_from_stage7'] = True
                        logger.info(f"Setting coming_from_stage7 flag to return to Stage 4 with step {next_step_no}")

                        # Force state update in session state
                        st.session_state['state'] = state

                        # Add a more visible indicator that we're advancing
                        st.success("🔄 Advancing to the next step... Please wait.")

                        # Add a small delay to ensure the state is properly updated
                        time.sleep(0.5)

                        # Call advance_to_next_step to move to the next step
                        # This will call st.rerun() internally if successful
                        advance_to_next_step()

                        # The code below will only execute if advance_to_next_step() fails
                        # and doesn't trigger a rerun
                        st.error("Failed to automatically advance to the next step. Please check the logs.")
                        logger.error("Failed to automatically advance to the next step after test execution")
                else:
                    # All steps processed - set the flag and force a rerun to show Stage 8 transition UI
                    logger.info("Stage 7: All steps completed, setting all_steps_done flag")
                    state.all_steps_done = True

                    # Store current step completion status
                    current_step_no = state.selected_step.get('Step No')
                    if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                        state.completed_steps = []
                    if current_step_no not in state.completed_steps:
                        state.completed_steps.append(current_step_no)
                        logger.info(f"State change: added final step {current_step_no} to completed_steps")

                    # Force state update in session state
                    st.session_state['state'] = state
                    logger.info("State change: all_steps_done = True, forcing rerun to show Stage 8 transition UI")

                    # Show a brief success message before rerun
                    st.success("✅ All test case steps have been processed! Preparing transition options...")

                    # Force rerun to show the Stage 8 transition UI at the top level
                    st.rerun()
            except Exception as e:
                # Handle Python exceptions during script execution
                st.error(f"Error running test script: {e}")
                import traceback
                traceback_str = traceback.format_exc()
                st.error(traceback_str)

                # Prepare error details for state management
                error_details = {
                    'error_message': f"Python exception during script execution: {str(e)}",
                    'returncode': -1,
                    'stdout': '',
                    'stderr': traceback_str,
                    'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
                    'step_no': state.selected_step.get('Step No', 'Unknown'),
                    'script_path': state.generated_script_path,
                    'exception_type': type(e).__name__
                }

                # Set error state to pause workflow
                _set_execution_error_safe(state, error_details)

                # Force state update in session state
                st.session_state['state'] = state

                logger.error(f"Python exception during script execution: {e}")
                logger.error(f"Traceback: {traceback_str}")

                # Return to let error acknowledgment UI handle the error
                return


def _display_error_acknowledgment_ui(state):
    """
    Display error acknowledgment UI when script execution fails.

    This function shows detailed error information and provides options for the user
    to acknowledge the error, retry execution, or view full logs before proceeding.

    Args:
        state (StateManager): The application state manager instance
    """
    import logging
    import os
    logger = logging.getLogger("ScriptWeaver.stage7")

    st.error("🚨 **Test Script Execution Failed**")
    st.markdown("The test script execution encountered an error. Please review the details below and choose how to proceed.")

    # Get error details from state
    error_details = getattr(state, 'execution_error_details', {})
    step_no = error_details.get('step_no', 'Unknown')
    error_message = error_details.get('error_message', 'Unknown error occurred')
    returncode = error_details.get('returncode', -1)
    timestamp = error_details.get('timestamp', 'Unknown')

    # Display error summary
    with st.expander("📋 Error Summary", expanded=True):
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Failed Step", step_no)
        with col2:
            st.metric("Exit Code", returncode)
        with col3:
            st.metric("Timestamp", timestamp)

        st.markdown(f"**Error Message:** {error_message}")

    # Display detailed error information
    with st.expander("🔍 Detailed Error Information", expanded=True):
        # Standard output
        stdout = error_details.get('stdout', '')
        if stdout:
            st.subheader("Standard Output")
            st.code(stdout, language="text")

        # Standard error
        stderr = error_details.get('stderr', '')
        if stderr:
            st.subheader("Error Output")
            st.code(stderr, language="text")

        # Script path
        script_path = error_details.get('script_path', '')
        if script_path:
            st.subheader("Failed Script")
            st.info(f"Script Path: {script_path}")

    # Action buttons
    st.markdown("---")
    st.markdown("### Choose how to proceed:")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("**Continue Workflow**")
        st.markdown("Acknowledge the error and return to test case selection")
        if st.button("✅ Acknowledge Error & Continue", key="acknowledge_error", use_container_width=True):
            logger.info(f"User acknowledged execution error for step {step_no}")

            # Mark error as acknowledged
            _acknowledge_execution_error_safe(state)

            # Clear error state
            _clear_execution_error_safe(state)

            # Set flag to return to Stage 4
            st.session_state['coming_from_stage7'] = True
            st.session_state['stage_progression_message'] = f"⚠️ Step {step_no} execution failed. Error acknowledged."

            # Force state update in session state
            st.session_state['state'] = state

            logger.info("Transitioning back to Stage 4 after error acknowledgment")
            st.rerun()

    with col2:
        st.markdown("**Retry Execution**")
        st.markdown("Clear the error and retry running the script")
        if st.button("🔄 Retry Execution", key="retry_execution", use_container_width=True):
            logger.info(f"User chose to retry execution for step {step_no}")

            # Clear error state to allow retry
            _clear_execution_error_safe(state)

            # Force state update in session state
            st.session_state['state'] = state

            logger.info("Cleared error state for retry")
            st.rerun()

    with col3:
        st.markdown("**View Full Logs**")
        st.markdown("Show complete execution logs and details")
        if st.button("📄 View Full Logs", key="view_full_logs", use_container_width=True):
            # Toggle expanded state for detailed view
            if 'show_full_error_logs' not in st.session_state:
                st.session_state['show_full_error_logs'] = True
            else:
                st.session_state['show_full_error_logs'] = not st.session_state['show_full_error_logs']
            st.rerun()

    # Show full logs if requested
    if st.session_state.get('show_full_error_logs', False):
        with st.expander("📄 Complete Execution Logs", expanded=True):
            st.markdown("**Complete Error Details:**")
            st.json(error_details)

            # Show script content if available
            script_path = error_details.get('script_path', '')
            if script_path and os.path.exists(script_path):
                st.markdown("**Script Content:**")
                try:
                    with open(script_path, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    st.code(script_content, language="python")
                except Exception as e:
                    st.error(f"Could not read script file: {e}")

    # Add helpful information
    st.markdown("---")
    st.info("💡 **Tip:** Review the error details above to understand what went wrong. You can modify the script in Phase 6 and retry, or acknowledge the error to continue with the next test case step.")


def _has_unacknowledged_error(state):
    """
    Check if there is an unacknowledged execution error.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        bool: True if there is an unacknowledged error, False otherwise
    """
    return (hasattr(state, 'execution_error_occurred') and
            getattr(state, 'execution_error_occurred', False) and
            not getattr(state, 'execution_error_acknowledged', True))


def _set_execution_error_safe(state, error_details):
    """
    Safely set execution error state, handling cases where StateManager might not have the new methods.

    Args:
        state (StateManager): The application state manager instance
        error_details (dict): Error details to store
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.stage7")

    try:
        # Try to use the new method
        if hasattr(state, 'set_execution_error'):
            state.set_execution_error(error_details)
        else:
            # Fallback: manually set the error state
            logger.warning("StateManager missing set_execution_error method, using fallback")

            # Add fields if they don't exist
            if not hasattr(state, 'execution_error_occurred'):
                state.execution_error_occurred = False
            if not hasattr(state, 'execution_error_acknowledged'):
                state.execution_error_acknowledged = False
            if not hasattr(state, 'execution_error_details'):
                state.execution_error_details = {}

            # Set error state manually
            state.execution_error_occurred = True
            state.execution_error_acknowledged = False
            state.execution_error_details = error_details.copy()

            logger.info(f"Fallback: Set execution error for step {error_details.get('step_no', 'Unknown')}")
            logger.error(f"Fallback: Error details - {error_details.get('error_message', 'No message')}")

    except Exception as e:
        logger.error(f"Failed to set execution error state: {e}")
        # Even if we can't set the error state, we should still show the error to the user
        st.error(f"Failed to set error state: {e}")


def _acknowledge_execution_error_safe(state):
    """
    Safely acknowledge execution error, handling cases where StateManager might not have the new methods.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        bool: True if error was acknowledged, False otherwise
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.stage7")

    try:
        if hasattr(state, 'acknowledge_execution_error'):
            return state.acknowledge_execution_error()
        else:
            # Fallback: manually acknowledge error
            logger.warning("StateManager missing acknowledge_execution_error method, using fallback")

            if (hasattr(state, 'execution_error_occurred') and
                state.execution_error_occurred and
                not getattr(state, 'execution_error_acknowledged', True)):

                state.execution_error_acknowledged = True
                logger.info("Fallback: Acknowledged execution error")
                return True
            return False

    except Exception as e:
        logger.error(f"Failed to acknowledge execution error: {e}")
        return False


def _clear_execution_error_safe(state):
    """
    Safely clear execution error state, handling cases where StateManager might not have the new methods.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        bool: True if error was cleared, False otherwise
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.stage7")

    try:
        if hasattr(state, 'clear_execution_error'):
            return state.clear_execution_error()
        else:
            # Fallback: manually clear error
            logger.warning("StateManager missing clear_execution_error method, using fallback")

            if hasattr(state, 'execution_error_occurred') and state.execution_error_occurred:
                state.execution_error_occurred = False
                state.execution_error_acknowledged = False
                if hasattr(state, 'execution_error_details'):
                    state.execution_error_details = {}
                logger.info("Fallback: Cleared execution error state")
                return True
            return False

    except Exception as e:
        logger.error(f"Failed to clear execution error: {e}")
        return False
